package it.masterzen.minebomb;

import it.masterzen.blockbreak.NBTItem;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataContainer;
import org.bukkit.persistence.PersistentDataType;

import java.util.ArrayList;
import java.util.List;

public class MineBombItem {

    public static final String HIDDEN_TAG_PREFIX = "\u00A70MBOMB:"; // §0MBOMB: (legacy support only)

    public static ItemStack create(BombType type, BombTier tier, int amount) {
        ItemStack is = new ItemStack(Material.FIREWORK_CHARGE, Math.max(1, amount));
        ItemMeta meta = is.getItemMeta();
        String typeName = (type == BombType.MONEY ? "Money" : "Tokens");
        meta.setDisplayName("§6Mine Bomb §7(" + typeName + ") §8T" + tier.getLevel());
        List<String> lore = new ArrayList<>();
        lore.add("§7Drop this while in your mine to redeem a reward");

        if (type == BombType.MONEY) {
            lore.add("§7Reward scales with your mine size and your current money");
        } else {
            lore.add("§7Reward scales with your mine size and your pick value");
        }

        lore.add("");
        // Hidden row removed as per requirement: we rely on the display name now
        meta.setLore(lore);
        meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);
        is.setItemMeta(meta);
        return is;
    }

    /**
     * Creates a refillable mine bomb with cooldown functionality
     * @param refillableType The refillable bomb type
     * @param tier The bomb tier
     * @param cooldownMinutes The cooldown in minutes
     * @return ItemStack of the refillable mine bomb
     */
    public static ItemStack createRefillable(RefillableBombType refillableType, BombTier tier, int cooldownMinutes) {
        if (!RefillableMineBombKeys.isInitialized()) {
            throw new IllegalStateException("RefillableMineBombKeys not initialized! Call RefillableMineBombKeys.initialize() first.");
        }

        ItemStack is = new ItemStack(Material.FIREWORK_CHARGE, 1);
        ItemMeta meta = is.getItemMeta();

        String typeName = refillableType.getDisplayName();
        meta.setDisplayName("§6§lRefillable Mine Bomb §7(" + typeName + ") §8T" + tier.getLevel());

        List<String> lore = new ArrayList<>();
        lore.add("§7Drop this while in your mine to redeem a reward");

        if (refillableType == RefillableBombType.REFILLABLE_MONEY) {
            lore.add("§7Reward scales with your mine size and your current money");
        } else {
            lore.add("§7Reward scales with your mine size and your pick value");
        }

        lore.add("");
        lore.add("§e§lREFILLABLE:");
        lore.add("§7You can use this item every §e" + CooldownUtils.formatCooldownDuration(cooldownMinutes));
        lore.add("§7This item will not be consumed when used");
        lore.add("");

        meta.setLore(lore);
        meta.addItemFlags(ItemFlag.HIDE_ATTRIBUTES);

        NBTItem nbtItem = new NBTItem(is);
        // Store refillable data in PersistentDataContainer
        PersistentDataContainer pdc = meta.getPersistentDataContainer();
        pdc.set(RefillableMineBombKeys.IS_REFILLABLE, PersistentDataType.BYTE, (byte) 1);
        pdc.set(RefillableMineBombKeys.REFILLABLE_TYPE, PersistentDataType.STRING, refillableType.name());
        pdc.set(RefillableMineBombKeys.REFILLABLE_TIER, PersistentDataType.INTEGER, tier.getLevel());
        pdc.set(RefillableMineBombKeys.COOLDOWN_DURATION, PersistentDataType.LONG, CooldownUtils.minutesToMillis(cooldownMinutes));

        is.setItemMeta(meta);
        return is;
    }

    public static boolean isMineBomb(ItemStack is) {
        if (is == null) return false;
        if (is.getType() != Material.FIREWORK_CHARGE) return false;
        if (!is.hasItemMeta()) return false;

        ItemMeta meta = is.getItemMeta();

        // Check for refillable mine bomb first
        if (isRefillableMineBomb(is)) {
            return true;
        }

        // Primary detection via display name
        if (meta.hasDisplayName()) {
            String name = ChatColor.stripColor(meta.getDisplayName());
            if (name != null && name.startsWith("Mine Bomb ")) {
                // Further light validation
                if (name.contains("(") && name.contains(")") && name.contains("T")) {
                    return true;
                }
            }
        }
        // Legacy detection via hidden lore tag (keep for backward compatibility)
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                if (ChatColor.stripColor(line).startsWith(ChatColor.stripColor(HIDDEN_TAG_PREFIX))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Checks if an ItemStack is a refillable mine bomb
     * @param is The ItemStack to check
     * @return true if it's a refillable mine bomb
     */
    public static boolean isRefillableMineBomb(ItemStack is) {
        if (is == null || !is.hasItemMeta()) return false;
        if (is.getType() != Material.FIREWORK_CHARGE) return false;

        ItemMeta meta = is.getItemMeta();

        // Check PersistentDataContainer first (most reliable)
        if (RefillableMineBombKeys.isInitialized()) {
            PersistentDataContainer pdc = meta.getPersistentDataContainer();
            if (pdc.has(RefillableMineBombKeys.IS_REFILLABLE, PersistentDataType.BYTE)) {
                return pdc.get(RefillableMineBombKeys.IS_REFILLABLE, PersistentDataType.BYTE) == 1;
            }
        }

        // Fallback to display name detection
        if (meta.hasDisplayName()) {
            String name = ChatColor.stripColor(meta.getDisplayName());
            return name != null && name.startsWith("Refillable Mine Bomb ");
        }

        return false;
    }

    public static ParsedBomb parse(ItemStack is) {
        if (is == null || !is.hasItemMeta()) return null;
        ItemMeta meta = is.getItemMeta();

        // Check if it's a refillable mine bomb first
        if (isRefillableMineBomb(is)) {
            return parseRefillable(is);
        }

        // Prefer parsing from display name
        if (meta.hasDisplayName()) {
            ParsedBomb parsed = parseFromName(meta.getDisplayName());
            if (parsed != null) return parsed;
        }
        // Fallback to legacy hidden lore parsing
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                String raw = ChatColor.stripColor(line);
                if (raw.startsWith(ChatColor.stripColor(HIDDEN_TAG_PREFIX))) {
                    String payload = raw.substring(ChatColor.stripColor(HIDDEN_TAG_PREFIX).length());
                    String[] parts = payload.split(":");
                    BombType type = BombType.fromString(parts[0]);
                    int tierInt = 1;
                    try { tierInt = Integer.parseInt(parts[1]); } catch (Exception ignored) {}
                    BombTier tier = BombTier.fromInt(tierInt);
                    if (type != null && tier != null) {
                        return new ParsedBomb(type, tier);
                    }
                }
            }
        }
        return null;
    }

    /**
     * Parses a refillable mine bomb and returns its data
     * @param is The refillable mine bomb ItemStack
     * @return ParsedRefillableBomb containing the bomb data, or null if invalid
     */
    public static ParsedRefillableBomb parseRefillable(ItemStack is) {
        if (!isRefillableMineBomb(is)) return null;

        ItemMeta meta = is.getItemMeta();
        PersistentDataContainer pdc = meta.getPersistentDataContainer();

        if (!RefillableMineBombKeys.isInitialized()) {
            // Fallback to display name parsing if keys not initialized
            return parseRefillableFromName(meta.getDisplayName());
        }

        try {
            String typeStr = pdc.get(RefillableMineBombKeys.REFILLABLE_TYPE, PersistentDataType.STRING);
            Integer tierInt = pdc.get(RefillableMineBombKeys.REFILLABLE_TIER, PersistentDataType.INTEGER);
            Long cooldownMillis = pdc.get(RefillableMineBombKeys.COOLDOWN_DURATION, PersistentDataType.LONG);
            Long lastUse = pdc.get(RefillableMineBombKeys.LAST_USE_TIME, PersistentDataType.LONG);

            RefillableBombType refillableType = RefillableBombType.valueOf(typeStr);
            BombTier tier = BombTier.fromInt(tierInt != null ? tierInt : 1);

            if (refillableType != null && tier != null && cooldownMillis != null) {
                return new ParsedRefillableBomb(refillableType, tier, cooldownMillis, lastUse);
            }
        } catch (Exception e) {
            // Fallback to display name parsing if PDC data is corrupted
            return parseRefillableFromName(meta.getDisplayName());
        }

        return null;
    }

    private static ParsedBomb parseFromName(String displayName) {
        if (displayName == null) return null;
        String name = ChatColor.stripColor(displayName).trim();
        // Expected pattern: "Mine Bomb (Money) T1" or "Mine Bomb (Tokens) T2"
        if (!name.startsWith("Mine Bomb ")) return null;
        int l = name.indexOf('(');
        int r = name.indexOf(')');
        int t = name.lastIndexOf('T');
        if (l < 0 || r < 0 || r <= l || t < 0 || t <= r) return null;
        String typeStr = name.substring(l + 1, r).trim();
        String tierStr = name.substring(t + 1).trim();
        BombType type = BombType.fromString(typeStr);
        int tierInt = 1;
        try { tierInt = Integer.parseInt(tierStr); } catch (Exception ignored) {}
        BombTier tier = BombTier.fromInt(tierInt);
        if (type == null || tier == null) return null;
        return new ParsedBomb(type, tier);
    }

    /**
     * Parses refillable mine bomb from display name (fallback method)
     */
    private static ParsedRefillableBomb parseRefillableFromName(String displayName) {
        if (displayName == null) return null;
        String name = ChatColor.stripColor(displayName).trim();
        // Expected pattern: "Refillable Mine Bomb (Money) T1" or "Refillable Mine Bomb (Tokens) T2"
        if (!name.startsWith("Refillable Mine Bomb ")) return null;
        int l = name.indexOf('(');
        int r = name.indexOf(')');
        int t = name.lastIndexOf('T');
        if (l < 0 || r < 0 || r <= l || t < 0 || t <= r) return null;
        String typeStr = name.substring(l + 1, r).trim();
        String tierStr = name.substring(t + 1).trim();
        RefillableBombType refillableType = RefillableBombType.fromString(typeStr);
        int tierInt = 1;
        try { tierInt = Integer.parseInt(tierStr); } catch (Exception ignored) {}
        BombTier tier = BombTier.fromInt(tierInt);
        if (refillableType == null || tier == null) return null;
        // Use default cooldown if parsing from name
        return new ParsedRefillableBomb(refillableType, tier, CooldownUtils.minutesToMillis(CooldownUtils.DEFAULT_COOLDOWN_MINUTES), null);
    }

    /**
     * Updates the lore of a refillable mine bomb to show current cooldown status
     * @param is The refillable mine bomb ItemStack
     * @return Updated ItemStack with current cooldown information
     */
    public static ItemStack updateRefillableLore(ItemStack is) {
        if (!isRefillableMineBomb(is)) return is;

        ParsedRefillableBomb parsed = parseRefillable(is);
        if (parsed == null) return is;

        ItemMeta meta = is.getItemMeta();
        List<String> lore = new ArrayList<>();

        lore.add("§7Drop this while in your mine to redeem a reward");

        if (parsed.refillableType == RefillableBombType.REFILLABLE_MONEY) {
            lore.add("§7Reward scales with your mine size and your current money");
        } else {
            lore.add("§7Reward scales with your mine size and your pick value");
        }

        lore.add("");
        lore.add("§e§lREFILLABLE:");
        lore.add("§7You can use this item every §e" + CooldownUtils.formatCooldownDuration((int) CooldownUtils.millisToMinutes(parsed.cooldownMillis)));
        lore.add("§7This item will not be consumed when used");

        // Add cooldown status
        if (parsed.isOnCooldown()) {
            long remaining = parsed.getRemainingCooldown();
            lore.add("");
            lore.add("§c§lCOOLDOWN:");
            lore.add("§cYou can use this again in §f" + CooldownUtils.formatRemainingTime(remaining));
        } else {
            lore.add("");
            lore.add("§a§lREADY TO USE!");
        }

        meta.setLore(lore);
        is.setItemMeta(meta);
        return is;
    }

    /**
     * Sets the last use time for a refillable mine bomb
     * @param is The refillable mine bomb ItemStack
     * @param timestamp The timestamp to set (current time if null)
     * @return Updated ItemStack with new last use time
     */
    public static ItemStack setLastUseTime(ItemStack is, Long timestamp) {
        if (!isRefillableMineBomb(is) || !RefillableMineBombKeys.isInitialized()) return is;

        ItemMeta meta = is.getItemMeta();
        PersistentDataContainer pdc = meta.getPersistentDataContainer();

        long useTime = timestamp != null ? timestamp : System.currentTimeMillis();
        pdc.set(RefillableMineBombKeys.LAST_USE_TIME, PersistentDataType.LONG, useTime);

        is.setItemMeta(meta);
        return updateRefillableLore(is); // Update lore to reflect new cooldown status
    }

    /**
     * Checks if a refillable mine bomb can be used (not on cooldown)
     * @param is The refillable mine bomb ItemStack
     * @return true if can be used, false if on cooldown
     */
    public static boolean canUseRefillable(ItemStack is) {
        if (!isRefillableMineBomb(is)) return false;

        ParsedRefillableBomb parsed = parseRefillable(is);
        return parsed != null && !parsed.isOnCooldown();
    }

    /**
     * Gets the remaining cooldown time for a refillable mine bomb
     * @param is The refillable mine bomb ItemStack
     * @return remaining cooldown in milliseconds, or 0 if not on cooldown
     */
    public static long getRemainingCooldown(ItemStack is) {
        if (!isRefillableMineBomb(is)) return 0;

        ParsedRefillableBomb parsed = parseRefillable(is);
        return parsed != null ? parsed.getRemainingCooldown() : 0;
    }

    /**
     * Resets the cooldown for a refillable mine bomb (admin function)
     * @param is The refillable mine bomb ItemStack
     * @return Updated ItemStack with reset cooldown
     */
    public static ItemStack resetCooldown(ItemStack is) {
        if (!isRefillableMineBomb(is) || !RefillableMineBombKeys.isInitialized()) return is;

        ItemMeta meta = is.getItemMeta();
        PersistentDataContainer pdc = meta.getPersistentDataContainer();

        // Remove the last use time to reset cooldown
        pdc.remove(RefillableMineBombKeys.LAST_USE_TIME);

        is.setItemMeta(meta);
        return updateRefillableLore(is); // Update lore to reflect reset status
    }

    public static class ParsedBomb {
        public final BombType type;
        public final BombTier tier;
        public ParsedBomb(BombType type, BombTier tier) {
            this.type = type; this.tier = tier;
        }
    }

    public static class ParsedRefillableBomb {
        public final RefillableBombType refillableType;
        public final BombTier tier;
        public final long cooldownMillis;
        public final Long lastUseTime; // null if never used

        public ParsedRefillableBomb(RefillableBombType refillableType, BombTier tier, long cooldownMillis, Long lastUseTime) {
            this.refillableType = refillableType;
            this.tier = tier;
            this.cooldownMillis = cooldownMillis;
            this.lastUseTime = lastUseTime;
        }

        public boolean isOnCooldown() {
            if (lastUseTime == null) return false;
            return !CooldownUtils.isCooldownExpired(lastUseTime, cooldownMillis);
        }

        public long getRemainingCooldown() {
            if (lastUseTime == null) return 0;
            return CooldownUtils.getRemainingCooldown(lastUseTime, cooldownMillis);
        }
    }
}
