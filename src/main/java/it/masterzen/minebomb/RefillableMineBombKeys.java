package it.masterzen.minebomb;

import org.bukkit.NamespacedKey;
import org.bukkit.plugin.Plugin;

/**
 * Container class for NamespacedKey constants used in refillable mine bomb system
 */
public class RefillableMineBombKeys {
    
    // Keys for PersistentDataContainer storage
    public static NamespacedKey LAST_USE_TIME;
    public static NamespacedKey COOLDOWN_DURATION;
    public static NamespacedKey REFILLABLE_TYPE;
    public static NamespacedKey REFILLABLE_TIER;
    public static NamespacedKey IS_REFILLABLE;

    /**
     * Initializes all NamespacedKey constants
     * Must be called during plugin initialization
     * @param plugin The plugin instance
     */
    public static void initialize(Plugin plugin) {
        LAST_USE_TIME = new NamespacedKey(plugin, "refillable_last_use");
        COOLDOWN_DURATION = new NamespacedKey(plugin, "refillable_cooldown");
        REFILLABLE_TYPE = new NamespacedKey(plugin, "refillable_type");
        REFILLABLE_TIER = new NamespacedKey(plugin, "refillable_tier");
        IS_REFILLABLE = new NamespacedKey(plugin, "is_refillable");
    }

    /**
     * Checks if the keys have been initialized
     * @return true if initialized, false otherwise
     */
    public static boolean isInitialized() {
        return LAST_USE_TIME != null && 
               COOLDOWN_DURATION != null && 
               REFILLABLE_TYPE != null && 
               REFILLABLE_TIER != null && 
               IS_REFILLABLE != null;
    }
}
